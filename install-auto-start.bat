@echo off
chcp 65001 >nul
title 安装自动启动服务

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    番茄小说下载器                            ║
echo ║                  自动启动服务安装器                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  此操作需要管理员权限
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限确认

REM 设置变量
set "SCRIPT_DIR=%~dp0"
set "SERVICE_NAME=FanqieProxyService"
set "SERVICE_SCRIPT=%SCRIPT_DIR%start-proxy-background.bat"

echo.
echo 📋 安装选项：
echo [1] 安装为Windows服务（推荐）
echo [2] 添加到启动文件夹
echo [3] 添加到注册表启动项
echo [4] 卸载自动启动
echo [5] 退出
echo.

choice /c 12345 /n /m "请选择安装方式 (1-5): "

if errorlevel 5 exit /b 0
if errorlevel 4 goto :uninstall
if errorlevel 3 goto :registry
if errorlevel 2 goto :startup_folder
if errorlevel 1 goto :windows_service

:windows_service
echo.
echo 🔧 安装为Windows服务...

REM 检查是否已安装
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  服务已存在，正在删除旧服务...
    sc stop "%SERVICE_NAME%" >nul 2>&1
    sc delete "%SERVICE_NAME%" >nul 2>&1
    timeout /t 2 >nul
)

REM 创建服务包装脚本
echo @echo off > "%SCRIPT_DIR%service-wrapper.bat"
echo cd /d "%SCRIPT_DIR%" >> "%SCRIPT_DIR%service-wrapper.bat"
echo node auto-proxy-service.js >> "%SCRIPT_DIR%service-wrapper.bat"

REM 使用NSSM创建服务（如果可用）
where nssm >nul 2>&1
if %errorlevel% equ 0 (
    echo 使用NSSM创建服务...
    nssm install "%SERVICE_NAME%" "%SCRIPT_DIR%service-wrapper.bat"
    nssm set "%SERVICE_NAME%" Description "番茄小说代理服务器"
    nssm set "%SERVICE_NAME%" Start SERVICE_AUTO_START
    sc start "%SERVICE_NAME%"
    echo ✅ Windows服务安装成功
) else (
    echo ❌ 未找到NSSM工具，请下载NSSM或选择其他安装方式
    echo 📥 NSSM下载地址：https://nssm.cc/download
)
goto :end

:startup_folder
echo.
echo 📁 添加到启动文件夹...

REM 获取启动文件夹路径
for /f "tokens=2*" %%a in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Startup 2^>nul') do set "STARTUP_FOLDER=%%b"

if not defined STARTUP_FOLDER (
    set "STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"
)

REM 创建启动脚本
echo @echo off > "%STARTUP_FOLDER%\FanqieProxy.bat"
echo cd /d "%SCRIPT_DIR%" >> "%STARTUP_FOLDER%\FanqieProxy.bat"
echo start /min "" "%SERVICE_SCRIPT%" >> "%STARTUP_FOLDER%\FanqieProxy.bat"

echo ✅ 已添加到启动文件夹: %STARTUP_FOLDER%
goto :end

:registry
echo.
echo 📝 添加到注册表启动项...

REM 添加注册表项
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "FanqieProxy" /t REG_SZ /d "\"%SERVICE_SCRIPT%\"" /f >nul

if %errorlevel% equ 0 (
    echo ✅ 已添加到注册表启动项
) else (
    echo ❌ 添加注册表启动项失败
)
goto :end

:uninstall
echo.
echo 🗑️  卸载自动启动...

REM 停止并删除Windows服务
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorlevel% equ 0 (
    echo 删除Windows服务...
    sc stop "%SERVICE_NAME%" >nul 2>&1
    sc delete "%SERVICE_NAME%" >nul 2>&1
    echo ✅ Windows服务已删除
)

REM 删除启动文件夹中的脚本
for /f "tokens=2*" %%a in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Startup 2^>nul') do set "STARTUP_FOLDER=%%b"
if not defined STARTUP_FOLDER set "STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"

if exist "%STARTUP_FOLDER%\FanqieProxy.bat" (
    del "%STARTUP_FOLDER%\FanqieProxy.bat"
    echo ✅ 启动文件夹脚本已删除
)

REM 删除注册表项
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "FanqieProxy" /f >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 注册表启动项已删除
)

REM 删除服务包装脚本
if exist "%SCRIPT_DIR%service-wrapper.bat" (
    del "%SCRIPT_DIR%service-wrapper.bat"
)

echo ✅ 自动启动已完全卸载

:end
echo.
echo 📋 安装完成！
echo.
echo 💡 使用说明：
echo • 代理服务器将在系统启动时自动运行
echo • 服务运行在端口3001
echo • 可以通过任务管理器查看运行状态
echo • 如需卸载，请重新运行此脚本选择卸载选项
echo.
pause
