import requests
import os
import json
import time
from typing import Optional, Dict, Any, Tuple, List
from ..constants import API_BASE_URL
from ..constants import CLIENT_VERSION


# 将全局导入注释掉，避免循环导入
# from ..base_system.context import GlobalContext


class NovelServerAPI:
    """小说服务器API交互类"""

    def __init__(self):
        # 在这里导入GlobalContext，避免循环导入
        from ..base_system.context import GlobalContext

        self.logger = GlobalContext.get_logger()
        self.config = GlobalContext.get_config()

        # 服务器API地址
        self.base_url = API_BASE_URL

        # API端点
        self.update_num_last_url = f"{self.base_url}/fq_token/update_num_last"
        self.get_quota_url = f"{self.base_url}/fq_token/get_quota"
        self.query_task_url = f"{self.base_url}/fq_token/query_task"  # 更新为正确的任务查询接口

        # 旧API端点，保留以兼容旧代码
        self.get_novel_url = f"{self.base_url}/fanqie/getNovelFanqieByNovelId"
        self.upload_novel_url = f"{self.base_url}/fanqie/uploadNovel"

    def request_novel_download(self, token: str, novel_id: str, novel_name: str,
                               novel_author: str, novel_desc: str,
                               chapter_ids: List[str] = None,
                               chapter_titles: List[str] = None,
                               format_choice: str = "txt") -> Dict[str, Any]:
        """请求下载小说，调用后端的update_num_last接口

        Args:
            token: 激活码
            novel_id: 小说ID
            novel_name: 小说名称
            novel_author: 小说作者
            novel_desc: 小说描述
            chapter_ids: 章节ID列表（番茄小说专用）
            chapter_titles: 章节标题列表（番茄小说专用）
            format_choice: 用户选择的文件格式 (txt/epub)

        Returns:
            API响应结果字典
        """
        try:
            # 创建请求数据
            novel_data = {
                "tokenName": token,
                "novelId": novel_id,
                "novelAuthor": novel_author,
                "novelName": novel_name,
                "novelDesc": novel_desc,
                "type": "pic",
                "clientVersion": CLIENT_VERSION,
                "formatChoice": format_choice  # 添加格式选择参数
            }

            # 如果是番茄小说ID，添加章节ID列表和章节标题列表
            if chapter_ids and len(novel_id) > 9:
                novel_data["novelChapterIds"] = chapter_ids
                # 如果提供了章节标题，也添加到请求中
                if chapter_titles:
                    novel_data["novelChapterTitles"] = chapter_titles

            self.logger.info(f"请求下载小说: {novel_name} (ID: {novel_id})")

            # 发送POST请求
            response = requests.post(
                self.update_num_last_url,
                json=novel_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                self.logger.info(f"下载请求成功，响应: {result}")
                return result
            else:
                self.logger.error(f"下载请求失败，状态码: {response.status_code}")
                return {
                    "code": str(response.status_code),
                    "msg": "请求失败",
                    "data": None
                }

        except Exception as e:
            self.logger.error(f"请求下载小说时发生错误: {str(e)}")
            return {
                "code": "500",
                "msg": f"请求异常: {str(e)}",
                "data": None
            }

    def check_download_task(self, task_id: str) -> Dict[str, Any]:
        """查询小说下载任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态信息字典
        """
        try:
            # 构建请求参数
            params = {
                "taskId": task_id
            }

            # 使用logger记录日志而不是print
            self.logger.debug(f"准备查询下载任务状态: {task_id}")

            # 发送GET请求
            response = requests.get(
                self.query_task_url,
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                self.logger.debug(f"查询任务状态成功，响应: {result}")

                # 解析任务状态数据
                if result.get("code") == 200:
                    task_info = result.get("data", {})

                    # 映射状态
                    status_map = {
                        "PENDING": "pending",
                        "PROCESSING": "processing",
                        "COMPLETED": "completed",
                        "FAILED": "failed"
                    }

                    raw_status = task_info.get("status", "")
                    status = status_map.get(raw_status, "unknown")

                    # 处理可能的进度值格式问题
                    try:
                        progress_value = task_info.get("progress", 0)
                        progress = int(progress_value) if progress_value else 0
                    except (ValueError, TypeError):
                        progress = 0

                    file_url = task_info.get("fileUrl", "")
                    error_msg = task_info.get("errorMsg", "")

                    return {
                        "status": status,
                        "progress": progress,
                        "fileUrl": file_url,
                        "errorMessage": error_msg,
                        "raw_status": raw_status  # 保留原始状态便于调试
                    }
                else:
                    error_msg = result.get("msg", "未知错误")
                    self.logger.error(f"查询任务状态失败: {error_msg}")
                    return {
                        "status": "error",
                        "errorMessage": error_msg
                    }
            else:
                self.logger.error(f"查询任务状态失败，状态码: {response.status_code}")
                return {
                    "status": "error",
                    "errorMessage": f"请求失败，状态码: {response.status_code}"
                }

        except Exception as e:
            self.logger.error(f"查询下载任务状态时发生错误: {str(e)}")
            return {
                "status": "error",
                "errorMessage": f"请求异常: {str(e)}"
            }

    def get_user_quota(self, token: str, client_version: str = None) -> Dict[str, Any]:
        """获取用户当前额度信息

        Args:
            token: 激活码
            client_version: 客户端版本号

        Returns:
            用户额度信息字典
        """
        try:
            # 构建请求参数
            params = {
                "tokenName": token
            }

            if client_version:
                params["clientVersion"] = client_version

            self.logger.info(f"获取用户额度信息: {token}")

            # 发送GET请求
            response = requests.get(
                self.get_quota_url,
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                self.logger.info(f"获取用户额度信息成功")
                return result
            else:
                self.logger.error(f"获取用户额度信息失败，状态码: {response.status_code}")
                return {
                    "code": str(response.status_code),
                    "msg": "请求失败",
                    "data": None
                }

        except Exception as e:
            self.logger.error(f"获取用户额度信息时发生错误: {str(e)}")
            return {
                "code": "500",
                "msg": f"请求异常: {str(e)}",
                "data": None
            }

    def upload_novel(self, novel_id: str, novel_info: Dict[str, Any], file_path: str, token: str) -> bool:
        """上传小说到服务器"""
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"文件不存在: {file_path}")
                return False

            # 构造JSON对象
            novelFanqie = {
                "novelId": novel_id,
                "novelName": novel_info.get("book_name", ""),
                "novelAuthor": novel_info.get("author", ""),
                "novelDesc": novel_info.get("description", ""),
                "novelChapterNum": str(novel_info.get("chapter_count", "0")),
                "createUserCode": token,
                "updateUserCode": token
            }

            # 构造多部分表单数据
            files = {
                "novel": (  # 对应@RequestParam("novel")
                    os.path.basename(file_path),
                    open(file_path, "rb"),
                    "application/octet-stream"
                ),
                "novelFanqie": (  # 对应@RequestBody
                    None,  # 无文件名
                    json.dumps(novelFanqie),
                    "application/json"
                )
            }

            self.logger.info(f"上传小说: {novel_info.get('book_name')}")

            # 发送POST请求
            response = requests.post(
                self.upload_novel_url,
                files=files,
                timeout=60
            )

            if response.status_code == 200:
                self.logger.info("上传成功")
                return True
            else:
                self.logger.error(f"失败，状态码: {response.status_code}, 响应: {response.text}")
                return False

        except Exception as e:
            self.logger.error(f"上传异常: {str(e)}")
            return False
        finally:
            if 'files' in locals():
                files["novel"][1].close()  # 确保文件句柄关闭

    def download_novel_file(self, url: str, save_path: str, format_type: str = "txt") -> Tuple[bool, str]:
        """从服务器下载小说文件

        Args:
            url: 文件URL
            save_path: 保存路径
            format_type: 文件格式，支持 "txt" 和 "epub"

        Returns:
            包含下载成功状态和最终文件路径的元组
        """
        try:
            # 确保保存目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            # 下载文件
            response = requests.get(url, stream=True, timeout=30)
            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                self.logger.info(f"小说文件下载成功: {save_path}")

                # 如果要求的格式是EPUB，但下载的是TXT文件，进行转换
                if format_type.lower() == "epub" and save_path.lower().endswith(".txt"):
                    try:
                        # 导入EPUB生成器
                        from ..book_parser.epub_generator import EpubGenerator

                        # 从文件名和路径中获取基础信息
                        book_name = os.path.basename(save_path).replace(".txt", "")
                        epub_path = save_path.replace(".txt", ".epub")

                        self.logger.info(f"开始将TXT转换为EPUB: {save_path} -> {epub_path}")

                        # 尝试检测文件编码
                        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'utf-16le', 'utf-16be']
                        content = None

                        for encoding in encodings:
                            try:
                                with open(save_path, 'r', encoding=encoding) as txt_file:
                                    content = txt_file.read()
                                self.logger.info(f"成功使用 {encoding} 编码读取文件")
                                break
                            except UnicodeDecodeError:
                                continue
                            except Exception as e:
                                self.logger.warning(f"尝试使用 {encoding} 编码读取文件时出错: {str(e)}")
                                continue

                        # 如果所有编码都失败，使用二进制读取然后尝试强制解码
                        if content is None:
                            self.logger.warning("所有编码都失败，尝试强制解码")
                            with open(save_path, 'rb') as f:
                                raw_content = f.read()
                                # 尝试从二进制数据强制解码
                                content = raw_content.decode('utf-8', errors='replace')

                        # 确保有内容
                        if not content:
                            raise ValueError("无法读取文件内容")

                        # 解析基本信息
                        author = "未知作者"
                        description = ""

                        # 尝试从TXT文件开头提取元数据
                        lines = content.split('\n')
                        book_info_section = []

                        # 收集前20行作为可能的元数据区域
                        for i, line in enumerate(lines[:20]):
                            if line.strip():
                                book_info_section.append(line)
                            # 如果遇到连续两个空行，可能是元数据区域结束
                            elif i > 0 and not lines[i - 1].strip():
                                break

                        # 从可能的元数据中提取信息
                        for line in book_info_section:
                            if "作者:" in line or "作者：" in line:
                                try:
                                    author = line.split(":", 1)[1].strip() if ":" in line else line.split("：", 1)[
                                        1].strip()
                                except IndexError:
                                    author = "未知作者"
                            elif "简介:" in line or "简介：" in line:
                                try:
                                    description = line.split(":", 1)[1].strip() if ":" in line else line.split("：", 1)[
                                        1].strip()
                                except IndexError:
                                    description = ""
                                # 查找后续的描述行
                                start_idx = book_info_section.index(line) + 1
                                for desc_line in book_info_section[start_idx:]:
                                    if ":" in desc_line or "：" in desc_line:
                                        break
                                    description += "\n" + desc_line

                        # 创建EPUB生成器
                        epub_gen = EpubGenerator(
                            identifier=f"novel_{book_name}",
                            title=book_name,
                            language="zh-CN",
                            author=author,
                            description=description,
                            publisher="番茄小说"
                        )

                        # 使用更安全的方法生成EPUB
                        try:
                            epub_gen.generate_from_text(content, book_name, author, description, epub_path)
                            self.logger.info(f"TXT转EPUB成功: {epub_path}")
                            # 返回EPUB文件路径
                            return True, epub_path
                        except Exception as e:
                            self.logger.error(f"生成EPUB失败: {str(e)}")
                            return True, save_path  # 即使转换失败，原TXT下载仍然成功

                    except Exception as e:
                        self.logger.error(f"TXT转EPUB失败: {str(e)}")
                        return True, save_path  # 即使转换失败，原TXT下载仍然成功

                return True, save_path
            else:
                self.logger.error(f"下载小说文件失败，状态码: {response.status_code}")
                return False, ""
        except Exception as e:
            self.logger.error(f"下载小说文件时发生错误: {str(e)}")
            return False, ""