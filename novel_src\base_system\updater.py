"""
updater.py - 自动更新模块
职责：检查、下载和安装软件更新
"""
import os
import sys
import json
import time
import hashlib
import tempfile
import platform
import subprocess
import threading
import requests
from pathlib import Path
from typing import Tuple, Dict, Optional, Union

from ..constants import VERSION
from .context import GlobalContext
from ..constants import API_BASE_URL

Base_URL = API_BASE_URL
# Base_URL = "http://localhost:9094"
class Updater:
    """自动更新管理器，负责检查新版本和下载更新"""

    # 更新服务器地址
    UPDATE_API = Base_URL + "/fq_version/check_update"
    UPDATE_DOWNLOAD_API = Base_URL + "/fq_version/download_update"

    def __init__(self):
        self.logger = GlobalContext.get_logger()
        self.config = GlobalContext.get_config()
        self.network = GlobalContext.get_network_client()
        self.current_version = VERSION
        self.update_info = None
        self.is_checking = False
        self.is_downloading = False
        self.download_progress = 0
        self.download_thread = None
        self.progress_callback = None
        self.update_finished_callback = None

        # 创建临时下载目录
        self.temp_dir = os.path.join(os.path.dirname(os.path.abspath(sys.argv[0])), "temp")
        os.makedirs(self.temp_dir, exist_ok=True)

    def check_update(self, auto_update=False) -> Tuple[bool, str, Dict]:
        """
        检查是否有更新
        参数:
            auto_update: 是否在检测到更新后自动下载安装
        返回: (是否有更新, 提示信息, 更新信息)
        """
        if self.is_checking:
            return False, "正在检查更新，请稍候...", {}

        self.is_checking = True
        try:
            # 构建请求参数
            params = {
                "currentVersion": self.current_version,
                "platform": platform.system(),
                "architecture": platform.machine(),
                "systemVersion": platform.version()
            }

            # 发送检查更新请求
            response = requests.get(
                self.UPDATE_API,
                params=params,
                timeout=10
            )
            response.raise_for_status()

            # 解析响应
            result = response.json()
            # print("版本==="+str(result))

            if result.get("code") != 200:
                return False, f"检查更新失败: {result.get('msg')}", {}

            update_info = result.get("data", {})

            # 检查是否有新版本
            if not update_info or not update_info.get("version"):
                return False, "当前已是最新版本", {}

            new_version = update_info.get("version")

            # 比较版本号
            if self._compare_versions(new_version, self.current_version) <= 0:
                return False, "当前已是最新版本", {}

            # 保存更新信息
            self.update_info = update_info

            # 检查是否为强制更新
            is_mandatory = update_info.get("isMandatory", False)
            if is_mandatory:
                update_info["isMandatory"] = True

            # 如果是自动更新模式，立即开始下载
            if auto_update:
                self.logger.info(f"自动更新模式：开始下载新版本 {new_version}")
                self.download_update(
                    finished_callback=lambda success, path_or_error:
                        self.install_update(path_or_error) if success else
                        self.logger.error(f"自动更新失败: {path_or_error}")
                )

            if is_mandatory:
                return True, f"发现新版本: {new_version} (强制更新)", update_info
            return True, f"发现新版本: {new_version}", update_info

        except Exception as e:
            self.logger.error(f"检查更新失败: {str(e)}")
            return False, f"检查更新失败: {str(e)}", {}
        finally:
            self.is_checking = False

    def download_update(self, progress_callback=None, finished_callback=None) -> bool:
        """
        下载更新
        progress_callback: 进度回调函数，参数为(进度百分比, 当前下载字节数, 总字节数)
        finished_callback: 下载完成回调函数，参数为(是否成功, 错误信息/目标路径)
        返回: 是否开始下载
        """
        if self.is_downloading:
            return False

        if not self.update_info:
            if finished_callback:
                try:
                    finished_callback(False, "未检测到更新信息，请先检查更新")
                except Exception as e:
                    self.logger.error(f"回调函数异常: {str(e)}")
            return False

        # 保存回调
        self.progress_callback = progress_callback
        self.update_finished_callback = finished_callback

        # 确保之前的下载线程已经结束
        if self.download_thread and self.download_thread.is_alive():
            try:
                self.download_thread.join(timeout=1.0)  # 等待1秒让线程结束
            except Exception as e:
                self.logger.warning(f"等待之前的下载线程时出错: {str(e)}")

        # 在新线程中下载
        self.download_thread = threading.Thread(target=self._download_thread)
        self.download_thread.daemon = True
        self.download_thread.start()

        return True

    def _download_thread(self):
        """下载线程"""
        self.is_downloading = True
        version = self.update_info.get("version") if self.update_info else None
        expected_hash = self.update_info.get("md5") if self.update_info else None
        file_handle = None
        temp_file_path = None
        max_retries = 3
        retry_count = 0
        retry_delay = 2  # 重试延迟时间（秒）
        download_success = False
        session = None

        # 捕获线程内所有异常，防止程序崩溃
        try:
            if not version:
                if self.update_finished_callback:
                    try:
                        self.update_finished_callback(False, "更新信息中没有版本号")
                    except Exception as e:
                        self.logger.error(f"回调函数异常: {str(e)}")
                return

            # 确保临时目录存在
            try:
                os.makedirs(self.temp_dir, exist_ok=True)
            except Exception as e:
                error_msg = f"创建临时目录失败: {str(e)}"
                self.logger.error(error_msg)
                self._safe_callback(self.update_finished_callback, False, error_msg)
                return

            # 构建目标文件名和路径
            file_name = f"fq_novel_update_v{version}.exe"
            target_path = os.path.join(self.temp_dir, file_name)

            # 使用临时文件下载，下载完成后再重命名
            temp_file_path = target_path + ".downloading"

            # 清理可能存在的旧临时文件
            if os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                except Exception as e:
                    self.logger.warning(f"无法删除旧的临时文件: {str(e)}")
                    # 使用一个新的临时文件名
                    temp_file_path = target_path + f".downloading.{int(time.time())}"

            # 构建下载URL - 直接使用标准格式请求API
            download_url = f"{self.UPDATE_DOWNLOAD_API}?version={version}"

            self.logger.info(f"开始下载更新，版本: {version}")
            self.logger.info(f"下载URL: {download_url}")
            self.logger.info(f"临时文件路径: {temp_file_path}")
            self.logger.info(f"最终保存路径: {target_path}")

            # 初始化下载标志和进度
            downloaded_size = 0
            total_size = 0
            response = None

            # 带重试的下载逻辑
            while not download_success and retry_count <= max_retries:
                if retry_count > 0:
                    self.logger.info(f"第 {retry_count} 次重试下载...")
                    self._safe_callback(self.progress_callback, 0, 0, 0)  # 重置进度
                    time.sleep(retry_delay)  # 延迟一段时间再重试

                try:
                    # 创建一个Session以便自动处理连接池和重试
                    if session is not None:
                        try:
                            session.close()
                        except:
                            pass

                    session = requests.Session()
                    session.mount('http://', requests.adapters.HTTPAdapter(max_retries=3))
                    session.mount('https://', requests.adapters.HTTPAdapter(max_retries=3))

                    # 使用HEAD请求先获取文件大小信息
                    try:
                        head_resp = session.head(download_url, timeout=10)
                        if head_resp and 'content-length' in head_resp.headers:
                            total_size = int(head_resp.headers.get('content-length', 0))
                            self.logger.info(f"文件大小: {total_size} 字节")
                        else:
                            self.logger.warning("无法获取文件大小信息")
                            total_size = 0
                    except Exception as e:
                        self.logger.warning(f"获取文件大小失败: {str(e)}")
                        total_size = 0

                    # 如果是断点续传，记录已下载的字节数
                    downloaded_size = 0
                    if os.path.exists(temp_file_path):
                        try:
                            downloaded_size = os.path.getsize(temp_file_path)
                            self.logger.info(f"已下载: {downloaded_size} 字节，继续下载")
                        except Exception as e:
                            self.logger.warning(f"获取已下载文件大小失败: {str(e)}")
                            downloaded_size = 0

                    # 设置断点续传的头部
                    headers = {}
                    if downloaded_size > 0:
                        headers['Range'] = f'bytes={downloaded_size}-'

                    # 执行下载，使用较小的超时时间并允许连接重置
                    response = session.get(
                        download_url,
                        headers=headers,
                        stream=True,
                        timeout=(30, 60)  # 连接超时30秒，读取超时60秒，减少读取超时时间可能有助于避免某些情况下的崩溃
                    )

                    if not response:
                        raise Exception("服务器无响应")

                    response.raise_for_status()

                    # 检查是否成功处理了断点续传
                    if downloaded_size > 0 and response.status_code == 206:
                        self.logger.info("服务器支持断点续传，从断点处继续下载")
                    elif downloaded_size > 0 and response.status_code == 200:
                        # 服务器不支持断点续传，从头开始下载
                        self.logger.warning("服务器不支持断点续传，从头开始下载")
                        downloaded_size = 0

                    # 以追加或写入模式打开文件
                    mode = 'ab' if downloaded_size > 0 else 'wb'
                    with open(temp_file_path, mode) as f:
                        file_handle = f

                        # 逐块下载文件，使用较小的块大小以降低内存使用量
                        chunk_size = 4096  # 更小的块大小
                        for chunk in response.iter_content(chunk_size=chunk_size):
                            if not chunk:  # 过滤掉keep-alive块
                                continue

                            # 检查是否已取消下载
                            if not self.is_downloading:
                                self.logger.info("下载已被取消")
                                return

                            try:
                                f.write(chunk)
                                downloaded_size += len(chunk)

                                # 计算并更新进度
                                if total_size > 0:
                                    progress = min(99.0, (downloaded_size / total_size) * 100)
                                else:
                                    progress = 0

                                self.download_progress = progress

                                # 每10个块（约40KB）回调一次进度更新，减少回调次数
                                if downloaded_size % (chunk_size * 10) < chunk_size:
                                    # 回调通知进度更新，捕获可能的异常
                                    self._safe_callback(self.progress_callback, progress, downloaded_size, total_size)
                            except Exception as write_error:
                                self.logger.error(f"写入文件时发生错误: {str(write_error)}")
                                raise write_error

                        # 最后更新一次进度
                        if total_size > 0:
                            progress = min(99.0, (downloaded_size / total_size) * 100)
                        else:
                            progress = 99.0

                        self.download_progress = progress
                        self._safe_callback(self.progress_callback, progress, downloaded_size, total_size)

                        # 确保所有数据都写入磁盘
                        try:
                            f.flush()
                            try:
                                os.fsync(f.fileno())
                            except Exception as e:
                                self.logger.warning(f"刷新文件缓冲区时出错: {str(e)}")
                        except Exception as e:
                            self.logger.warning(f"写入缓存到磁盘时出错: {str(e)}")

                    # 如果代码执行到这里，说明下载成功完成
                    download_success = True

                except (requests.ConnectionError, requests.Timeout, requests.ChunkedEncodingError) as e:
                    # 这些是网络相关的错误，可以重试
                    retry_count += 1
                    error_type = type(e).__name__
                    self.logger.warning(f"下载时发生网络错误 ({error_type}): {str(e)}，剩余重试次数: {max_retries - retry_count}")

                    # 关闭并释放资源
                    if response:
                        try:
                            response.close()
                        except:
                            pass

                    if file_handle is not None:
                        try:
                            file_handle.close()
                        except:
                            pass
                    file_handle = None

                    # 如果已经达到最大重试次数，则报告错误
                    if retry_count > max_retries:
                        error_msg = f"下载失败，网络错误: {str(e)}"
                        self.logger.error(error_msg)
                        self._safe_callback(self.update_finished_callback, False, error_msg)
                        return

                except Exception as e:
                    # 其他错误不重试，直接失败
                    error_msg = f"下载过程中发生错误: {str(e)}"
                    self.logger.error(error_msg)
                    self._safe_callback(self.update_finished_callback, False, error_msg)

                    # 关闭并释放资源
                    if response:
                        try:
                            response.close()
                        except:
                            pass

                    return

                finally:
                    # 确保文件句柄已关闭
                    if file_handle is not None:
                        try:
                            file_handle.close()
                        except:
                            pass
                    file_handle = None

                    # 确保响应对象已关闭
                    if response:
                        try:
                            response.close()
                        except:
                            pass
                        response = None

            # 最终检查 - 下载成功才继续
            if not download_success:
                error_msg = "下载失败，已达到最大重试次数"
                self.logger.error(error_msg)
                self._safe_callback(self.update_finished_callback, False, error_msg)
                return

            # 下载成功后的处理

            # 检查临时文件是否存在
            if not os.path.exists(temp_file_path):
                error_msg = "下载失败：临时文件未创建"
                self.logger.error(error_msg)
                self._safe_callback(self.update_finished_callback, False, error_msg)
                return

            # 获取实际下载的文件大小
            try:
                actual_size = os.path.getsize(temp_file_path)
                self.logger.info(f"下载完成，文件大小: {actual_size}字节")
            except Exception as e:
                error_msg = f"获取下载文件大小失败: {str(e)}"
                self.logger.error(error_msg)
                self._safe_callback(self.update_finished_callback, False, error_msg)
                return

            # 如果服务器返回了文件大小信息，验证文件大小
            if total_size > 0 and actual_size < total_size:
                error_msg = f"文件大小不匹配: 期望={total_size}字节, 实际={actual_size}字节"
                self.logger.error(error_msg)
                self._safe_callback(self.update_finished_callback, False, error_msg)
                return

            # 在重命名前先休眠一下，确保系统有时间完全释放文件句柄
            time.sleep(0.5)

            # 移除可能存在的旧文件
            target_rename_success = False
            if os.path.exists(target_path):
                try:
                    os.remove(target_path)
                    self.logger.info(f"已删除旧的更新文件: {target_path}")
                except Exception as e:
                    error_msg = f"无法删除旧的更新文件: {str(e)}"
                    self.logger.error(error_msg)

                    # 尝试使用不同的名称保存新文件
                    original_target_path = target_path
                    target_path = os.path.join(self.temp_dir, f"fq_novel_update_v{version}_{int(time.time())}.exe")
                    self.logger.info(f"将使用新的目标路径: {target_path}")

            # 将临时文件重命名为目标文件，使用多次尝试
            rename_attempts = 3
            for attempt in range(rename_attempts):
                try:
                    os.rename(temp_file_path, target_path)
                    target_rename_success = True
                    self.logger.info(f"文件重命名成功: {target_path}")
                    break
                except Exception as e:
                    self.logger.error(f"第{attempt+1}次尝试重命名文件失败: {str(e)}")
                    if attempt < rename_attempts - 1:
                        time.sleep(1)  # 休眠一秒后重试
                        # 再次尝试关闭所有可能持有该文件的引用
                        if file_handle is not None:
                            try:
                                file_handle.close()
                            except:
                                pass
                            file_handle = None

                        # 尝试使用系统GC回收，可能有助于释放资源
                        try:
                            import gc
                            gc.collect()
                        except:
                            pass
                    else:
                        error_msg = f"重命名文件失败: {str(e)}"
                        self.logger.error(error_msg)

                        # 尝试复制文件而不是重命名
                        try:
                            import shutil
                            shutil.copy2(temp_file_path, target_path)
                            self.logger.info("使用复制方式代替重命名成功")

                            # 成功复制后尝试删除临时文件
                            try:
                                os.remove(temp_file_path)
                            except Exception as del_err:
                                self.logger.warning(f"删除临时文件时出错: {str(del_err)}")

                            target_rename_success = True
                        except Exception as copy_err:
                            self.logger.error(f"复制文件也失败: {str(copy_err)}")
                            self._safe_callback(self.update_finished_callback, False, error_msg)
                            return

            # 如果所有的重命名尝试都失败了
            if not target_rename_success:
                error_msg = "所有重命名尝试都失败"
                self.logger.error(error_msg)
                self._safe_callback(self.update_finished_callback, False, error_msg)
                return

            # 验证文件完整性
            if expected_hash:
                try:
                    file_hash = self._get_file_md5(target_path)
                    if file_hash != expected_hash:
                        error_msg = f"文件校验失败: 期望={expected_hash}, 实际={file_hash}"
                        self.logger.error(error_msg)
                        self._safe_callback(self.update_finished_callback, False, "文件校验失败，请重新下载")
                        return
                    self.logger.info(f"文件MD5校验成功: {file_hash}")
                except Exception as e:
                    error_msg = f"校验文件时出错: {str(e)}"
                    self.logger.error(error_msg)
                    self._safe_callback(self.update_finished_callback, False, error_msg)
                    return

            # 下载成功
            self.logger.info(f"更新包下载成功: {target_path}")

            # 更新进度为100%，表示完全完成
            self._safe_callback(self.progress_callback, 100, actual_size, actual_size)

            # 通知下载完成
            self._safe_callback(self.update_finished_callback, True, target_path)

        except Exception as e:
            error_msg = f"下载更新失败: {str(e)}"
            self.logger.error(error_msg)
            self._safe_callback(self.update_finished_callback, False, error_msg)
        finally:
            # 确保文件句柄已关闭
            if file_handle is not None:
                try:
                    file_handle.close()
                except:
                    pass

            # 关闭会话
            if session is not None:
                try:
                    session.close()
                except:
                    pass

            # 清理临时文件（如果存在且下载失败）
            if not download_success and temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                    self.logger.info(f"已清理临时文件: {temp_file_path}")
                except:
                    pass

            self.is_downloading = False

    def _safe_callback(self, callback_func, *args):
        """安全地调用回调函数，捕获任何异常"""
        if callback_func:
            try:
                callback_func(*args)
            except Exception as e:
                self.logger.error(f"回调函数异常: {str(e)}")

    def install_update(self, update_file_path: str) -> bool:
        """
        安装更新，启动下载好的安装程序
        """
        try:
            if not update_file_path or not isinstance(update_file_path, str):
                self.logger.error(f"无效的更新文件路径: {update_file_path}")
                return False

            if not os.path.exists(update_file_path):
                self.logger.error(f"安装文件不存在: {update_file_path}")
                return False

            # 检查文件是否可执行
            if not os.access(update_file_path, os.X_OK) and platform.system() != "Windows":
                try:
                    os.chmod(update_file_path, 0o755)  # 添加执行权限
                    self.logger.info(f"为更新文件添加了执行权限")
                except Exception as e:
                    self.logger.warning(f"无法为更新文件添加执行权限: {str(e)}")

            self.logger.info(f"准备安装更新: {update_file_path}")

            # 安全启动安装程序
            try:
                # 在Windows上，使用startupinfo隐藏控制台窗口
                if platform.system() == "Windows":
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = 0  # SW_HIDE

                    # 使用更安全的方式启动进程
                    try:
                        # Windows 10+建议使用这种方式
                        proc = subprocess.Popen(
                            [update_file_path],
                            shell=False,  # 设为False可能更安全
                            startupinfo=startupinfo,
                            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.DETACHED_PROCESS,
                            close_fds=True  # 关闭继承的文件描述符
                        )
                    except Exception as e1:
                        self.logger.warning(f"首选启动方式失败: {str(e1)}，尝试备用方式")
                        # 备用启动方式
                        proc = subprocess.Popen(
                            [update_file_path],
                            shell=True,
                            startupinfo=startupinfo
                        )
                else:
                    # 在非Windows系统上，使用nohup或类似机制启动
                    proc = subprocess.Popen(
                        [update_file_path],
                        shell=False,
                        start_new_session=True,
                        close_fds=True
                    )

                self.logger.info(f"安装程序已启动，PID: {proc.pid}")
            except Exception as e:
                self.logger.error(f"启动安装程序失败: {str(e)}")
                # 尝试使用系统默认方式打开
                try:
                    if platform.system() == "Windows":
                        os.startfile(update_file_path)
                    else:
                        subprocess.Popen(['xdg-open', update_file_path])
                    self.logger.info("使用系统默认方式打开安装程序")
                except Exception as e2:
                    self.logger.error(f"使用系统默认方式打开安装程序也失败: {str(e2)}")
                    return False

            # 等待足够长的时间，确保新进程已启动
            time.sleep(2)

            # 执行清理操作
            self._prepare_for_exit()

            # 退出当前程序
            self.logger.info("安装程序已启动，当前程序将退出")
            try:
                sys.exit(0)
            except SystemExit:
                # 正常的退出异常，直接传递
                raise
            except Exception as e:
                # 其他异常，记录并尝试强制退出
                self.logger.error(f"退出程序时发生异常: {str(e)}")
                os._exit(0)  # 强制退出

            return True
        except Exception as e:
            self.logger.error(f"安装更新失败: {str(e)}")
            return False

    def _prepare_for_exit(self):
        """
        在退出程序前进行必要的清理工作
        """
        try:
            # 关闭所有可能的网络连接
            if hasattr(self.network, 'close') and callable(self.network.close):
                try:
                    self.network.close()
                except Exception as e:
                    self.logger.error(f"关闭网络连接时出错: {str(e)}")

            # 释放其他资源...
            self.logger.info("程序退出前清理工作完成")
        except Exception as e:
            self.logger.error(f"退出前清理工作失败: {str(e)}")

    def exit_on_window_close(self):
        """
        当用户关闭窗口时退出程序
        对于强制更新场景，用户不应该关闭窗口而不更新
        """
        if self.update_info and self.update_info.get("isMandatory", False):
            self.logger.info("强制更新模式：用户关闭窗口，程序将退出")
            sys.exit(0)
        else:
            # 非强制更新模式，也需要退出程序
            self.logger.info("用户关闭窗口，程序将退出")
            sys.exit(0)
    
    def _compare_versions(self, version1: str, version2: str) -> int:
        """
        比较两个版本号
        返回: 1表示version1>version2，0表示相等，-1表示version1<version2
        """
        v1_parts = list(map(int, version1.split('.')))
        v2_parts = list(map(int, version2.split('.')))
        
        # 补齐位数
        length = max(len(v1_parts), len(v2_parts))
        v1_parts.extend([0] * (length - len(v1_parts)))
        v2_parts.extend([0] * (length - len(v2_parts)))
        
        # 比较
        for i in range(length):
            if v1_parts[i] > v2_parts[i]:
                return 1
            elif v1_parts[i] < v2_parts[i]:
                return -1
        
        return 0
    
    def _get_file_md5(self, file_path: str) -> str:
        """计算文件MD5哈希值"""
        md5 = hashlib.md5()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b''):
                md5.update(chunk)
        return md5.hexdigest() 