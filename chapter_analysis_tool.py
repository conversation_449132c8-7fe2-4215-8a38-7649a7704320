#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节分析工具
用于分析txt文件的章节结构，帮助优化epub生成参数
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def analyze_txt_file(file_path, config=None):
    """分析txt文件的章节结构"""
    try:
        # 设置默认配置
        default_config = {
            'min_chapter_length': 800,
            'max_chapter_length': 15000,
            'strict_mode': False,
            'auto_merge': True,
            'auto_split': True,
            'debug_mode': True
        }
        
        if config:
            default_config.update(config)
        
        # 创建简单的logger
        import logging
        logging.basicConfig(level=logging.INFO, format='%(message)s')
        logger = logging.getLogger('analyzer')
        
        # 模拟GlobalContext
        class MockGlobalContext:
            @staticmethod
            def get_logger():
                return logger
            
            @staticmethod
            def get_config():
                class MockConfig:
                    default_save_dir = Path.cwd() / 'temp'
                return MockConfig()
        
        # 临时替换
        import novel_src.book_parser.epub_generator as epub_module
        epub_module.GlobalContext = MockGlobalContext
        
        from novel_src.book_parser.epub_generator import EpubGenerator
        
        # 读取文件
        print(f"📖 正在分析文件: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return None
        
        # 尝试多种编码读取文件
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'utf-16le', 'utf-16be']
        content = None
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"✅ 成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"⚠️ 使用 {encoding} 编码读取失败: {e}")
                continue
        
        if not content:
            print("❌ 无法读取文件内容")
            return None
        
        # 创建分析器
        analyzer = EpubGenerator(
            identifier="analysis",
            title="分析测试",
            language="zh-CN",
            parsing_config=default_config
        )
        
        # 执行分析
        print("\n" + "="*50)
        analysis = analyzer.analyze_text_structure(content)
        print("="*50)
        
        if analysis:
            print(f"\n📊 分析结果总结:")
            print(f"   📄 文件总行数: {analysis['total_lines']}")
            print(f"   📍 内容开始行: {analysis['content_start_line']}")
            print(f"   📝 有效内容行: {analysis['processed_lines']}")
            print(f"   📚 检测章节数: {analysis['detected_chapters']}")
            print(f"   📏 平均章节长度: {analysis['average_chapter_length']:.0f} 字符")
            
            if analysis['chapter_titles']:
                print(f"\n📋 章节列表:")
                for i, (title, length) in enumerate(zip(analysis['chapter_titles'], analysis['chapter_lengths']), 1):
                    status = "✅" if length >= default_config['min_chapter_length'] else "⚠️"
                    print(f"   {i:2d}. {status} {title} ({length} 字符)")
            
            # 提供优化建议
            print(f"\n💡 优化建议:")
            
            if analysis['detected_chapters'] == 0:
                print("   - 未检测到章节，建议检查文件格式或降低识别阈值")
            elif analysis['detected_chapters'] < 5:
                print("   - 章节数较少，可能存在漏检，建议启用debug_mode查看详情")
            
            short_chapters = sum(1 for length in analysis['chapter_lengths'] if length < default_config['min_chapter_length'])
            if short_chapters > 0:
                print(f"   - 发现{short_chapters}个短章节，建议启用auto_merge自动合并")
            
            long_chapters = sum(1 for length in analysis['chapter_lengths'] if length > default_config['max_chapter_length'])
            if long_chapters > 0:
                print(f"   - 发现{long_chapters}个长章节，建议启用auto_split自动分割")
        
        return analysis
        
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析txt文件的章节结构')
    parser.add_argument('file_path', help='要分析的txt文件路径')
    parser.add_argument('--min-length', type=int, default=800, help='最小章节长度')
    parser.add_argument('--max-length', type=int, default=15000, help='最大章节长度')
    parser.add_argument('--strict', action='store_true', help='启用严格模式')
    parser.add_argument('--no-merge', action='store_true', help='禁用自动合并')
    parser.add_argument('--no-split', action='store_true', help='禁用自动分割')
    
    args = parser.parse_args()
    
    # 构建配置
    config = {
        'min_chapter_length': args.min_length,
        'max_chapter_length': args.max_length,
        'strict_mode': args.strict,
        'auto_merge': not args.no_merge,
        'auto_split': not args.no_split,
        'debug_mode': True
    }
    
    print("🔍 章节结构分析工具")
    print(f"📁 目标文件: {args.file_path}")
    print(f"⚙️ 配置参数: {config}")
    print()
    
    # 执行分析
    result = analyze_txt_file(args.file_path, config)
    
    if result:
        print(f"\n✅ 分析完成！")
        
        # 生成测试EPUB
        test_epub = args.file_path.replace('.txt', '_test.epub')
        print(f"\n📖 生成测试EPUB: {test_epub}")
        
        try:
            # 重新读取文件内容
            with open(args.file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 创建测试生成器
            from novel_src.book_parser.epub_generator import EpubGenerator
            
            generator = EpubGenerator(
                identifier="test",
                title=Path(args.file_path).stem,
                language="zh-CN",
                author="测试作者",
                description="章节解析测试",
                parsing_config=config
            )
            
            success = generator.generate_from_text(
                content,
                Path(args.file_path).stem,
                "测试作者",
                "章节解析测试",
                test_epub
            )
            
            if success:
                file_size = os.path.getsize(test_epub)
                print(f"✅ 测试EPUB生成成功: {test_epub} ({file_size} 字节)")
            else:
                print("❌ 测试EPUB生成失败")
                
        except Exception as e:
            print(f"❌ 生成测试EPUB时出错: {e}")
    else:
        print("❌ 分析失败")


if __name__ == "__main__":
    main()
