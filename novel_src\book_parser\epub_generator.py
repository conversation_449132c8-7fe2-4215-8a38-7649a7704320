from ebooklib import epub
import re

from ..base_system.context import GlobalContext


class EpubGenerator:
    def __init__(self, identifier, title, language="en", author=None, description=None, publisher=None,
                 parsing_config=None):
        """
        初始化EPUB书籍对象
        :param identifier: 书籍唯一标识符
        :param title: 书籍标题
        :param language: 语言代码（默认'en'）
        :param author: 作者（可选）
        :param description: 描述（可选）
        :param publisher: 出版社（可选）
        :param parsing_config: 章节解析配置（可选）
        """
        self.book = epub.EpubBook()
        self.logger = GlobalContext.get_logger()

        # 设置基本元数据
        self.book.set_identifier(identifier)
        self.book.set_title(title)
        self.book.set_language(language)

        # 尝试设置封面，但只在能找到封面文件时才设置
        try:
            cover_path = str(GlobalContext.get_config().default_save_dir / f"{title}.jpg")
            self.logger.debug(f"尝试加载封面: {cover_path}")

            with open(cover_path, 'rb') as cover_file:
                cover_content = cover_file.read()  # 获取二进制内容

            # 只有当封面内容非空时才设置封面
            if cover_content and len(cover_content) > 0:
                self.book.set_cover("cover.jpg", cover_content)
                self.logger.debug("封面设置成功")
            else:
                self.logger.warning("封面文件为空，跳过设置封面")
        except FileNotFoundError:
            self.logger.warning(f"封面文件未找到: {cover_path}，跳过设置封面")
        except Exception as e:
            self.logger.warning(f"读取封面失败: {str(e)}，跳过设置封面")

        # 添加可选元数据
        if author:
            self.book.add_author(author)
        if publisher:
            self.book.add_metadata("DC", "publisher", publisher)
        if description:
            self.book.add_metadata("DC", "description", description)

        style = '''
        @namespace epub "http://www.idpf.org/2007/ops";
        body { font-family: "Noto Serif CJK SC", SimSun, serif; }
        h1 { text-align: center; margin: 1em 0; }
        p { text-indent: 2em; margin: 0.5em 0; }
        '''
        nav_css = epub.EpubItem(
            uid="style_nav",
            file_name="style/nav.css",
            media_type="text/css",
            content=style
        )
        self.book.add_item(nav_css)

        self.chapters = []
        self._file_counter = 1  # 用于生成自动文件名

        # 章节解析配置
        self.parsing_config = parsing_config or {
            'min_chapter_length': 800,      # 最小章节长度
            'max_chapter_length': 15000,    # 最大章节长度
            'strict_mode': False,           # 严格模式
            'auto_merge': True,             # 自动合并短章节
            'auto_split': True,             # 自动分割长章节
            'debug_mode': False             # 调试模式
        }

    def add_chapter(self, title, content, file_name=None):
        """
        添加章节到书籍
        :param title: 章节标题
        :param content: HTML内容（不带<html>标签）
        :param file_name: 自定义文件名（可选）
        """
        # 生成自动文件名（如果未提供）
        if not file_name:
            file_name = f"chap_{self._file_counter:02d}.xhtml"
            self._file_counter += 1

        # 创建章节对象
        chapter = epub.EpubHtml(
            title=title, file_name=file_name, lang=self.book.language
        )
        chapter.content = content

        # 添加到书籍
        self.book.add_item(chapter)
        self.chapters.append(chapter)

    def generate(self, output_path, toc=None):
        """
        生成EPUB文件
        :param output_path: 输出文件路径
        :param toc: 自定义目录结构（可选）
        """
        try:
            # 设置默认目录（如果未提供）
            if not toc:
                self.book.toc = [(epub.Section("目录"), self.chapters)]
            else:
                self.book.toc = toc

            # 添加导航文件
            self.book.add_item(epub.EpubNcx())
            self.book.add_item(epub.EpubNav())

            # 设置书脊（spine）
            self.book.spine = ["nav"] + self.chapters

            # 生成文件
            self.logger.info(f"开始生成EPUB文件: {output_path}")
            epub.write_epub(output_path, self.book)
            self.logger.info(f"EPUB文件生成成功: {output_path}")
        except Exception as e:
            self.logger.error(f"EPUB生成失败: {str(e)}")
            raise

    def add_metadata(self, namespace, name, value):
        """
        添加自定义元数据
        :param namespace: 命名空间（如'DC'）
        :param name: 元数据名称
        :param value: 元数据值
        """
        self.book.add_metadata(namespace, name, value)

    def generate_from_text(self, text_content, title, author, description, output_path):
        """
        从纯文本内容生成EPUB电子书
        :param text_content: 文本内容
        :param title: 书籍标题
        :param author: 作者
        :param description: 描述
        :param output_path: 输出路径
        """
        try:
            # 添加作者和描述
            if author:
                self.book.add_author(author)
            if description:
                self.book.add_metadata("DC", "description", description)

            # 添加优化的CSS样式
            style = '''
            @namespace epub "http://www.idpf.org/2007/ops";

            body {
                font-family: "Noto Serif CJK SC", "Microsoft YaHei", "SimSun", serif;
                line-height: 1.8;
                margin: 1.5em;
                font-size: 1em;
                color: #333;
                background-color: #fff;
            }

            h1 {
                font-size: 1.6em;
                font-weight: bold;
                border-bottom: 2px solid #ddd;
                padding-bottom: 0.5em;
                text-align: center;
                margin: 1.5em 0 2em;
                color: #2c3e50;
                page-break-before: always;
            }

            p {
                text-indent: 2em;
                margin: 0.8em 0;
                line-height: 1.8;
                text-align: justify;
                word-wrap: break-word;
                orphans: 2;
                widows: 2;
            }

            .content {
                margin-top: 1.5em;
                text-align: justify;
            }

            .intro {
                font-style: italic;
                background-color: #f8f9fa;
                padding: 1em;
                border-left: 4px solid #007bff;
                margin: 1em 0;
            }

            /* 分页控制 */
            .chapter {
                page-break-before: always;
            }

            /* 移动设备优化 */
            @media screen and (max-width: 600px) {
                body {
                    margin: 1em;
                    font-size: 0.9em;
                }
                h1 {
                    font-size: 1.4em;
                }
            }
            '''
            nav_css = epub.EpubItem(
                uid="style_custom",
                file_name="style/custom.css",
                media_type="text/css",
                content=style
            )
            self.book.add_item(nav_css)

            # 添加简介章节
            intro_html = epub.EpubHtml(title="简介", file_name="intro.xhtml", lang=self.book.language)
            intro_content = f"""
            <h1>简介</h1>
            <div class="content">
                <p>{description}</p>
                <p>作者：{author}</p>
            </div>
            """
            intro_html.content = intro_content
            intro_html.add_item(nav_css)
            self.book.add_item(intro_html)
            self.chapters.append(intro_html)

            # 解析文本内容生成章节
            if self.parsing_config.get('debug_mode'):
                self.debug_chapter_parsing(text_content)

            chapters_data = self._parse_chapters_from_text(text_content)

            # 评估章节质量
            self._evaluate_chapter_quality(chapters_data)

            # 添加所有章节到电子书
            for i, (chapter_title, chapter_content) in enumerate(chapters_data):
                # 创建章节
                chapter = epub.EpubHtml(
                    title=chapter_title,
                    file_name=f"chap_{i + 1:03d}.xhtml",
                    lang=self.book.language
                )

                # 优化HTML内容生成
                html_content = self._generate_chapter_html(chapter_title, chapter_content)
                chapter.content = html_content
                chapter.add_item(nav_css)

                # 添加到书籍
                self.book.add_item(chapter)
                self.chapters.append(chapter)

            # 设置优化的目录结构
            self.book.toc = self._generate_optimized_toc()

            # 添加导航文件
            self.book.add_item(epub.EpubNcx())
            self.book.add_item(epub.EpubNav())

            # 设置书脊（spine）
            self.book.spine = ["nav"] + self.chapters

            # 生成文件
            self.logger.info(f"开始生成EPUB文件: {output_path}")
            epub.write_epub(output_path, self.book)
            self.logger.info(f"EPUB文件生成成功: {output_path}")

        except Exception as e:
            self.logger.error(f"从文本生成EPUB失败: {str(e)}")
            # 不抛出异常，避免程序崩溃
            return False

        return True

    def _parse_chapters_from_text(self, text_content):
        """
        从文本内容中解析章节 - 优化版本
        :param text_content: 文本内容
        :return: 章节列表，每个元素为(标题, 内容)元组
        """
        try:
            chapters = []
            lines = text_content.split('\n')

            # 跳过前面可能包含元数据的行
            start_index = self._find_content_start(lines)

            # 预处理：清理和标准化行
            processed_lines = self._preprocess_lines(lines[start_index:])

            # 智能章节识别
            chapter_indices = self._identify_chapter_boundaries(processed_lines)

            if not chapter_indices:
                # 如果没有找到章节，尝试其他策略
                chapter_indices = self._fallback_chapter_detection(processed_lines)

            # 根据章节边界提取章节内容
            chapters = self._extract_chapters_by_boundaries(processed_lines, chapter_indices)

            # 后处理：清理和验证章节
            chapters = self._post_process_chapters(chapters)

            # 智能章节合并和分割（如果启用）
            if self.parsing_config.get('auto_merge') or self.parsing_config.get('auto_split'):
                chapters = self._smart_chapter_merge_split(chapters)

            # 如果仍然没有章节，作为单章处理
            if not chapters:
                self.logger.warning("未检测到明确的章节标记，将整个内容作为单章处理")
                content_text = "\n".join([line for line in processed_lines if line.strip()])
                book_title = getattr(self.book, 'title', "正文")
                chapters.append((book_title, content_text))

            self.logger.info(f"成功解析出 {len(chapters)} 个章节")
            return chapters

        except Exception as e:
            self.logger.error(f"解析章节失败: {str(e)}")
            # 返回一个包含全部内容的单一章节
            book_title = getattr(self.book, 'title', "正文")
            return [(book_title, text_content)]

    def _find_content_start(self, lines):
        """找到正文开始位置，跳过元数据"""
        start_index = 0
        metadata_indicators = [
            "===========", "----------", "书名:", "作者:", "简介:",
            "来源:", "更新时间:", "字数:", "状态:", "类型:"
        ]

        for i, line in enumerate(lines[:50]):  # 最多检查前50行
            line_stripped = line.strip()

            # 检查是否包含元数据标识符
            if any(indicator in line_stripped for indicator in metadata_indicators):
                start_index = i + 1
                continue

            # 如果遇到明显的章节标题，停止搜索
            if self._is_likely_chapter_title(line_stripped):
                start_index = i
                break

            # 如果行长度超过100且包含实际内容，可能是正文开始
            if len(line_stripped) > 100 and not any(char in line_stripped for char in "：:="):
                start_index = i
                break

        return start_index

    def _preprocess_lines(self, lines):
        """预处理文本行，清理和标准化"""
        processed = []
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 移除多余的空白字符
            line = re.sub(r'\s+', ' ', line)

            # 标准化章节标题格式
            line = self._normalize_chapter_title(line)

            processed.append(line)

        return processed

    def _normalize_chapter_title(self, line):
        """标准化章节标题格式"""
        # 标准化数字格式
        line = re.sub(r'第\s*(\d+)\s*章', r'第\1章', line)

        # 处理中文数字
        chinese_nums = {
            '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
            '六': '6', '七': '7', '八': '8', '九': '9', '十': '10',
            '零': '0'
        }

        for chinese, arabic in chinese_nums.items():
            if f'第{chinese}章' in line:
                line = line.replace(f'第{chinese}章', f'第{arabic}章')

        return line

    def _identify_chapter_boundaries(self, lines):
        """智能识别章节边界"""
        chapter_indices = []

        # 多种章节标题模式
        patterns = [
            # 标准格式：第X章
            re.compile(r'^第\s*\d+\s*章', re.IGNORECASE),
            # 中文数字：第一章、第二章等
            re.compile(r'^第[零一二三四五六七八九十百千万]+章', re.IGNORECASE),
            # 数字开头：1. 2. 1、2、等
            re.compile(r'^\d+[\s\.\-、:：]\s*\S+', re.IGNORECASE),
            # 卷、篇、部等
            re.compile(r'^第[零一二三四五六七八九十百千万\d]+[卷篇部集]', re.IGNORECASE),
            # Chapter格式
            re.compile(r'^Chapter\s*\d+', re.IGNORECASE),
            # 楔子、序章、尾声等特殊章节
            re.compile(r'^(楔子|序章|序言|前言|尾声|后记|番外)', re.IGNORECASE)
        ]

        for i, line in enumerate(lines):
            is_chapter = False

            # 检查是否匹配任何章节模式
            for pattern in patterns:
                if pattern.match(line):
                    # 额外验证：章节标题不应该太长
                    if len(line) <= 100:
                        is_chapter = True
                        break

            # 启发式检测：短行且包含"章"字（但不与正则重复）
            if not is_chapter and (len(line) <= 50 and
                ('章' in line or 'Chapter' in line) and
                not self._is_likely_content_line(line)):
                is_chapter = True

            if is_chapter and i not in chapter_indices:
                chapter_indices.append(i)

        # 去重并排序
        chapter_indices = sorted(list(set(chapter_indices)))

        return chapter_indices

    def _fallback_chapter_detection(self, lines):
        """备用章节检测策略"""
        chapter_indices = []

        # 策略1：基于行长度和内容特征
        for i, line in enumerate(lines):
            if (len(line) <= 30 and
                len(line) >= 3 and
                not self._is_likely_content_line(line) and
                ('第' in line or '章' in line or line.isdigit())):
                chapter_indices.append(i)

        # 策略2：基于段落分隔
        if not chapter_indices:
            # 寻找明显的段落分隔
            for i in range(1, len(lines) - 1):
                prev_line = lines[i-1] if i > 0 else ""
                curr_line = lines[i]
                next_line = lines[i+1] if i < len(lines) - 1 else ""

                # 如果当前行很短，前后都是长行，可能是章节标题
                if (len(curr_line) <= 40 and
                    len(prev_line) > 80 and
                    len(next_line) > 80 and
                    not self._is_likely_content_line(curr_line)):
                    chapter_indices.append(i)

        return chapter_indices

    def _is_likely_chapter_title(self, line):
        """判断是否可能是章节标题"""
        if not line or len(line) > 100:
            return False

        # 明确的章节标识符
        chapter_indicators = [
            r'^第\s*\d+\s*章',  # 第1章、第 1 章等
            r'^第[零一二三四五六七八九十百千万]+章',  # 第一章、第二章等
            r'^\d+[\s\.\-、:：]',  # 1. 2、3：等
            r'^Chapter\s*\d+',  # Chapter 1等
            r'^(楔子|序章|序言|前言|尾声|后记|番外)$'  # 特殊章节，精确匹配
        ]

        for pattern in chapter_indicators:
            if re.search(pattern, line, re.IGNORECASE):
                return True

        # 额外检查：包含"章"且长度适中的行
        if ('章' in line and len(line) <= 30 and
            not self._is_likely_content_line(line)):
            return True

        return False

    def _is_likely_content_line(self, line):
        """判断是否可能是正文内容行"""
        if not line:
            return False

        # 包含标点符号较多的行更可能是内容
        punctuation_count = sum(1 for char in line if char in '，。！？；：""''（）【】《》')

        # 长行且包含标点符号
        if len(line) > 50 and punctuation_count >= 3:
            return True

        # 包含常见的内容词汇
        content_indicators = ['说道', '说着', '想着', '看着', '听着', '走着']
        if any(indicator in line for indicator in content_indicators):
            return True

        return False

    def _extract_chapters_by_boundaries(self, lines, chapter_indices):
        """根据章节边界提取章节内容"""
        chapters = []

        if not chapter_indices:
            return chapters

        # 去重并排序章节索引
        chapter_indices = sorted(list(set(chapter_indices)))

        for i, start_idx in enumerate(chapter_indices):
            # 确定章节结束位置
            end_idx = chapter_indices[i + 1] if i + 1 < len(chapter_indices) else len(lines)

            # 提取章节标题和内容
            if start_idx >= len(lines):
                continue

            title = lines[start_idx].strip()
            content_lines = []

            # 收集章节内容（跳过标题行）
            for line_idx in range(start_idx + 1, end_idx):
                if line_idx < len(lines):
                    line = lines[line_idx].strip()
                    if line:  # 只添加非空行
                        content_lines.append(line)

            content = '\n'.join(content_lines).strip()

            # 过滤掉过短的章节（可能是误识别）
            min_length = self.parsing_config.get('min_chapter_length', 800) // 4  # 降低阈值用于初步过滤
            if len(content) >= min_length:
                chapters.append((title, content))
                self.logger.debug(f"提取章节: {title} (长度: {len(content)})")
            else:
                self.logger.debug(f"跳过过短章节: {title} (长度: {len(content)})")

        return chapters

    def _post_process_chapters(self, chapters):
        """后处理章节，清理和验证"""
        processed_chapters = []

        for i, (title, content) in enumerate(chapters):
            # 智能清理和标准化标题
            title = self._clean_and_standardize_title(title, i + 1)

            # 清理内容
            content = self._clean_chapter_content(content, title)

            if content:  # 只添加有内容的章节
                processed_chapters.append((title, content))

        return processed_chapters

    def _clean_and_standardize_title(self, title, chapter_num):
        """清理和标准化章节标题"""
        if not title:
            return f"第{chapter_num}章"

        title = title.strip()

        # 移除多余的标点符号
        title = re.sub(r'^[：:：\-\s]+', '', title)
        title = re.sub(r'[：:：\-\s]+$', '', title)

        # 标准化章节号格式
        # 将 "第3章：新的开始" 转换为 "第3章 新的开始"
        title = re.sub(r'^(第\d+章)[：:：\-\s]*', r'\1 ', title)

        # 处理纯数字开头的标题
        if re.match(r'^\d+[\.\-、:：\s]', title):
            # 提取数字和标题部分
            match = re.match(r'^(\d+)[\.\-、:：\s]+(.+)', title)
            if match:
                num, title_part = match.groups()
                title = f"第{num}章 {title_part.strip()}"

        # 处理Chapter格式
        if title.lower().startswith('chapter'):
            match = re.match(r'^chapter\s*(\d+)\s*(.*)', title, re.IGNORECASE)
            if match:
                num, title_part = match.groups()
                if title_part.strip():
                    title = f"第{num}章 {title_part.strip()}"
                else:
                    title = f"第{num}章"

        # 确保特殊章节格式正确
        special_chapters = ['楔子', '序章', '序言', '前言', '尾声', '后记', '番外']
        for special in special_chapters:
            if special in title and title != special:
                title = special
                break

        return title

    def _clean_chapter_content(self, content, title):
        """清理章节内容"""
        if not content:
            return ""

        content = content.strip()
        content_lines = content.split('\n')
        cleaned_lines = []

        for line in content_lines:
            line = line.strip()
            if not line:
                continue

            # 移除内容开头的重复标题
            if line == title or line == title.replace(' ', ''):
                continue

            # 移除明显的分隔符行
            if re.match(r'^[=\-\*]{3,}$', line):
                continue

            # 移除页码等无关内容
            if re.match(r'^\d+$', line) and len(line) <= 3:
                continue

            cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def _generate_chapter_html(self, title, content):
        """生成优化的章节HTML内容"""
        # 转义HTML特殊字符
        title = self._escape_html(title)

        # 处理段落
        paragraphs = self._process_paragraphs(content)

        # 生成优化的HTML
        html_content = f"""<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops">
<head>
    <title>{title}</title>
    <link rel="stylesheet" type="text/css" href="../style/custom.css"/>
    <meta charset="utf-8"/>
</head>
<body>
    <div class="chapter">
        <h1>{title}</h1>
        <div class="content">
            {paragraphs}
        </div>
    </div>
</body>
</html>"""

        return html_content

    def _escape_html(self, text):
        """转义HTML特殊字符"""
        if not text:
            return ""

        html_escape_table = {
            "&": "&amp;",
            "<": "&lt;",
            ">": "&gt;",
            '"': "&quot;",
            "'": "&#x27;"
        }

        for char, escape in html_escape_table.items():
            text = text.replace(char, escape)

        return text

    def _process_paragraphs(self, content):
        """处理段落，生成HTML段落标签"""
        if not content:
            return ""

        # 按行分割内容
        lines = content.split('\n')
        paragraphs = []
        current_paragraph = []

        for line in lines:
            line = line.strip()
            if not line:
                # 空行表示段落结束
                if current_paragraph:
                    paragraph_text = ' '.join(current_paragraph)
                    if paragraph_text:
                        paragraphs.append(f"<p>{self._escape_html(paragraph_text)}</p>")
                    current_paragraph = []
            else:
                current_paragraph.append(line)

        # 处理最后一个段落
        if current_paragraph:
            paragraph_text = ' '.join(current_paragraph)
            if paragraph_text:
                paragraphs.append(f"<p>{self._escape_html(paragraph_text)}</p>")

        return '\n        '.join(paragraphs)

    def _evaluate_chapter_quality(self, chapters):
        """评估章节解析质量并提供调试信息"""
        if not chapters:
            self.logger.warning("📊 章节质量评估: 未找到任何章节")
            return

        self.logger.info(f"📊 章节质量评估报告:")
        self.logger.info(f"   总章节数: {len(chapters)}")

        # 统计章节长度
        lengths = [len(content) for _, content in chapters]
        avg_length = sum(lengths) / len(lengths) if lengths else 0
        min_length = min(lengths) if lengths else 0
        max_length = max(lengths) if lengths else 0

        self.logger.info(f"   平均章节长度: {avg_length:.0f} 字符")
        self.logger.info(f"   最短章节: {min_length} 字符")
        self.logger.info(f"   最长章节: {max_length} 字符")

        # 检查异常章节
        short_chapters = [(title, len(content)) for title, content in chapters if len(content) < 500]
        if short_chapters:
            self.logger.warning(f"   发现 {len(short_chapters)} 个可能过短的章节:")
            for title, length in short_chapters[:5]:  # 只显示前5个
                self.logger.warning(f"     - {title}: {length} 字符")

        # 检查标题格式
        title_patterns = {}
        for title, _ in chapters:
            if re.match(r'第\d+章', title):
                title_patterns['标准格式'] = title_patterns.get('标准格式', 0) + 1
            elif re.match(r'第[零一二三四五六七八九十百千万]+章', title):
                title_patterns['中文数字'] = title_patterns.get('中文数字', 0) + 1
            elif re.match(r'^\d+', title):
                title_patterns['数字开头'] = title_patterns.get('数字开头', 0) + 1
            else:
                title_patterns['其他格式'] = title_patterns.get('其他格式', 0) + 1

        self.logger.info(f"   标题格式分布: {title_patterns}")

    def debug_chapter_parsing(self, text_content, max_lines=50):
        """调试章节解析过程，输出详细信息"""
        self.logger.info("🔍 开始调试章节解析过程...")

        lines = text_content.split('\n')
        start_index = self._find_content_start(lines)
        processed_lines = self._preprocess_lines(lines[start_index:])

        self.logger.info(f"📄 文件总行数: {len(lines)}")
        self.logger.info(f"📍 内容开始位置: 第{start_index + 1}行")
        self.logger.info(f"📝 处理后行数: {len(processed_lines)}")

        # 显示前几行内容
        self.logger.info("📖 文件开头内容预览:")
        for i, line in enumerate(processed_lines[:max_lines]):
            line_type = "[章节]" if self._is_likely_chapter_title(line) else "[内容]"
            display_line = line[:80] + ('...' if len(line) > 80 else '')
            self.logger.info(f"   {i+1:3d}: {line_type} {display_line}")

        # 识别章节边界
        chapter_indices = self._identify_chapter_boundaries(processed_lines)
        self.logger.info(f"🎯 识别到的章节边界: {chapter_indices}")

        if chapter_indices:
            self.logger.info("📚 章节标题预览:")
            for i, idx in enumerate(chapter_indices[:10]):  # 只显示前10个
                title = processed_lines[idx] if idx < len(processed_lines) else "未知"
                self.logger.info(f"   第{i+1}章: {title}")

        return chapter_indices

    def analyze_text_structure(self, text_content):
        """
        分析文本结构，用于调试和优化章节解析
        :param text_content: 文本内容
        :return: 分析结果字典
        """
        try:
            lines = text_content.split('\n')
            start_index = self._find_content_start(lines)
            processed_lines = self._preprocess_lines(lines[start_index:])

            # 执行调试解析
            chapter_indices = self.debug_chapter_parsing(text_content)

            # 提取章节进行分析
            chapters = self._extract_chapters_by_boundaries(processed_lines, chapter_indices)
            chapters = self._post_process_chapters(chapters)

            # 返回分析结果
            analysis = {
                'total_lines': len(lines),
                'content_start_line': start_index + 1,
                'processed_lines': len(processed_lines),
                'detected_chapters': len(chapters),
                'chapter_titles': [title for title, _ in chapters],
                'chapter_lengths': [len(content) for _, content in chapters],
                'average_chapter_length': sum(len(content) for _, content in chapters) / len(chapters) if chapters else 0
            }

            return analysis

        except Exception as e:
            self.logger.error(f"文本结构分析失败: {str(e)}")
            return None

    def _smart_chapter_merge_split(self, chapters):
        """
        智能章节合并和分割
        - 合并过短的章节
        - 分割过长的章节
        """
        if not chapters:
            return chapters

        processed_chapters = []
        min_chapter_length = self.parsing_config.get('min_chapter_length', 800)
        max_chapter_length = self.parsing_config.get('max_chapter_length', 15000)
        auto_merge = self.parsing_config.get('auto_merge', True)
        auto_split = self.parsing_config.get('auto_split', True)

        i = 0
        while i < len(chapters):
            title, content = chapters[i]
            content_length = len(content)

            # 处理过短章节 - 尝试与下一章合并
            if (auto_merge and
                content_length < min_chapter_length and
                i + 1 < len(chapters)):
                next_title, next_content = chapters[i + 1]

                # 检查是否应该合并
                if self._should_merge_chapters(title, content, next_title, next_content):
                    merged_title = title
                    merged_content = content + "\n\n" + next_content
                    processed_chapters.append((merged_title, merged_content))
                    self.logger.debug(f"合并章节: {title} + {next_title}")
                    i += 2  # 跳过下一章
                    continue

            # 处理过长章节 - 尝试分割
            if auto_split and content_length > max_chapter_length:
                split_chapters = self._split_long_chapter(title, content)
                processed_chapters.extend(split_chapters)
                self.logger.debug(f"分割长章节: {title} -> {len(split_chapters)} 个子章节")
            else:
                processed_chapters.append((title, content))

            i += 1

        return processed_chapters

    def _should_merge_chapters(self, title1, content1, title2, content2):
        """判断两个章节是否应该合并"""
        # 如果第一章太短且第二章也不长，考虑合并
        if len(content1) < 500 and len(content2) < 3000:
            return True

        # 如果标题相似（可能是分段的同一章），考虑合并
        if self._titles_are_similar(title1, title2):
            return True

        return False

    def _titles_are_similar(self, title1, title2):
        """判断两个标题是否相似"""
        # 提取章节号
        def extract_chapter_num(title):
            match = re.search(r'第(\d+)章', title)
            return int(match.group(1)) if match else None

        num1 = extract_chapter_num(title1)
        num2 = extract_chapter_num(title2)

        # 如果是连续章节号，可能需要合并
        if num1 and num2 and abs(num1 - num2) == 1:
            return True

        return False

    def _split_long_chapter(self, title, content):
        """分割过长的章节"""
        # 按段落分割
        paragraphs = content.split('\n\n')
        if len(paragraphs) < 2:
            # 如果没有明显段落分隔，按句子分割
            sentences = re.split(r'[。！？]', content)
            paragraphs = [s + '。' for s in sentences if s.strip()]

        # 计算每个子章节的目标长度
        target_length = 8000  # 目标长度
        sub_chapters = []
        current_content = []
        current_length = 0
        part_num = 1

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 如果添加这个段落会超过目标长度，先保存当前内容
            if current_length + len(paragraph) > target_length and current_content:
                sub_title = f"{title} (第{part_num}部分)"
                sub_content = '\n\n'.join(current_content)
                sub_chapters.append((sub_title, sub_content))

                current_content = [paragraph]
                current_length = len(paragraph)
                part_num += 1
            else:
                current_content.append(paragraph)
                current_length += len(paragraph)

        # 添加最后一部分
        if current_content:
            if len(sub_chapters) == 0:
                # 如果只有一部分，保持原标题
                sub_chapters.append((title, '\n\n'.join(current_content)))
            else:
                sub_title = f"{title} (第{part_num}部分)"
                sub_content = '\n\n'.join(current_content)
                sub_chapters.append((sub_title, sub_content))

        return sub_chapters if sub_chapters else [(title, content)]

    def _generate_optimized_toc(self):
        """生成优化的目录结构"""
        if not self.chapters:
            return []

        toc_entries = []

        # 按章节类型分组
        intro_chapters = []
        main_chapters = []
        epilogue_chapters = []

        for chapter in self.chapters:
            title = chapter.title.lower()
            if any(keyword in title for keyword in ['简介', '序言', '前言', '楔子']):
                intro_chapters.append(chapter)
            elif any(keyword in title for keyword in ['尾声', '后记', '番外', '结语']):
                epilogue_chapters.append(chapter)
            else:
                main_chapters.append(chapter)

        # 构建目录结构
        if intro_chapters:
            if len(intro_chapters) == 1:
                toc_entries.extend(intro_chapters)
            else:
                toc_entries.append((epub.Section("前言部分"), intro_chapters))

        if main_chapters:
            if len(main_chapters) <= 20:
                # 章节不多时，直接列出
                toc_entries.extend(main_chapters)
            else:
                # 章节较多时，按组分类
                groups = self._group_chapters_by_volume(main_chapters)
                for group_name, group_chapters in groups:
                    toc_entries.append((epub.Section(group_name), group_chapters))

        if epilogue_chapters:
            if len(epilogue_chapters) == 1:
                toc_entries.extend(epilogue_chapters)
            else:
                toc_entries.append((epub.Section("后记部分"), epilogue_chapters))

        return toc_entries

    def _group_chapters_by_volume(self, chapters):
        """将章节按卷分组"""
        groups = []
        current_group = []
        group_size = 20  # 每组最多20章

        for i, chapter in enumerate(chapters):
            current_group.append(chapter)

            if len(current_group) >= group_size or i == len(chapters) - 1:
                # 计算组的起始和结束章节号
                start_num = self._extract_chapter_number(current_group[0].title)
                end_num = self._extract_chapter_number(current_group[-1].title)

                if start_num and end_num:
                    group_name = f"第{start_num}-{end_num}章"
                else:
                    group_name = f"第{len(groups) + 1}部分"

                groups.append((group_name, current_group[:]))
                current_group = []

        return groups

    def _extract_chapter_number(self, title):
        """从章节标题中提取章节号"""
        # 匹配阿拉伯数字
        match = re.search(r'第(\d+)章', title)
        if match:
            return int(match.group(1))

        # 匹配中文数字（简单版本）
        chinese_to_num = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
        }

        for chinese, num in chinese_to_num.items():
            if f'第{chinese}章' in title:
                return num

        return None
