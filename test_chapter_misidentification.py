#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试章节误识别修复效果
专门测试"顺理成章"等成语被误识别为章节标题的问题
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def create_problematic_test_content():
    """创建包含容易误识别内容的测试文本"""
    test_content = """书名：容易误识别的小说
作者：测试作者
简介：这是一个包含容易被误识别为章节的内容的测试小说
====================

第一章 开始

主人公开始了他的冒险之旅。这一切都显得顺理成章，没有什么特别的地方。

他走在路上，心想这样的发展真是理所当然。班长辞职，由副班长接任，顺理成章。

第二章 发展

故事继续发展。主人公遇到了各种挑战，但他都能够应对自如。

有人说："这样的结果真是水到渠成。"另一个人回答："是啊，一切都按部就班。"

第三章 转折

突然，情况发生了变化。原本以为会按章行事的计划出现了意外。

"这下可不是按图索骥那么简单了。"主人公自言自语道。

第四章 解决

经过一番努力，主人公终于找到了解决问题的方法。这个过程虽然曲折，但结果令人满意。

一切都回到了正轨，就像是有章可循一样自然。

第五章 结局

故事接近尾声。主人公回顾这段经历，感慨万千。

"人生就是这样，"他想，"有时候需要循章摘句地仔细分析，有时候需要大胆创新。"

尾声

这就是整个故事。从开始到结束，每一步都有其道理，可以说是有章有法。

虽然过程中遇到了各种困难，但最终还是达成了目标。这就是成长的意义。
"""
    return test_content


def test_misidentification_fix():
    """测试误识别修复效果"""
    print("🔍 测试章节误识别修复效果...")
    
    # 创建简单的logger
    import logging
    logging.basicConfig(level=logging.INFO, format='%(message)s')
    logger = logging.getLogger('test')
    
    # 模拟GlobalContext
    class MockGlobalContext:
        @staticmethod
        def get_logger():
            return logger
        
        @staticmethod
        def get_config():
            class MockConfig:
                default_save_dir = Path.cwd() / 'temp'
            return MockConfig()
    
    # 临时替换
    import novel_src.book_parser.epub_generator as epub_module
    epub_module.GlobalContext = MockGlobalContext
    
    from novel_src.book_parser.epub_generator import EpubGenerator
    
    # 创建测试内容
    test_content = create_problematic_test_content()
    
    # 创建生成器（启用调试模式）
    parsing_config = {
        'min_chapter_length': 200,
        'max_chapter_length': 5000,
        'strict_mode': True,  # 启用严格模式
        'auto_merge': False,  # 禁用自动合并以便观察原始识别结果
        'auto_split': False,  # 禁用自动分割
        'debug_mode': True
    }
    
    generator = EpubGenerator(
        identifier="test_misid",
        title="误识别测试",
        language="zh-CN",
        author="测试作者",
        description="测试章节误识别修复",
        parsing_config=parsing_config
    )
    
    print("\n📊 分析包含易误识别内容的文本...")
    analysis = generator.analyze_text_structure(test_content)
    
    if analysis:
        print(f"\n✅ 分析结果:")
        print(f"   检测到章节数: {analysis['detected_chapters']}")
        print(f"   章节标题:")
        
        for i, title in enumerate(analysis['chapter_titles'], 1):
            # 检查是否包含成语
            contains_idiom = any(idiom in title for idiom in [
                '顺理成章', '水到渠成', '按部就班', '按章行事', 
                '按图索骥', '有章可循', '循章摘句', '有章有法'
            ])
            
            status = "❌ 误识别" if contains_idiom else "✅ 正确"
            print(f"   {i:2d}. {status} {title}")
        
        # 统计误识别情况
        misidentified = sum(1 for title in analysis['chapter_titles'] 
                          if any(idiom in title for idiom in [
                              '顺理成章', '水到渠成', '按部就班', '按章行事',
                              '按图索骥', '有章可循', '循章摘句', '有章有法'
                          ]))
        
        print(f"\n📈 修复效果评估:")
        print(f"   总识别章节: {analysis['detected_chapters']}")
        print(f"   误识别章节: {misidentified}")
        print(f"   准确率: {((analysis['detected_chapters'] - misidentified) / analysis['detected_chapters'] * 100):.1f}%" if analysis['detected_chapters'] > 0 else "N/A")
        
        if misidentified == 0:
            print("🎉 完美！没有发现误识别的章节标题")
        else:
            print(f"⚠️ 仍有 {misidentified} 个误识别，需要进一步优化")
    
    # 生成测试EPUB
    print(f"\n📖 生成测试EPUB...")
    test_output = "test_misidentification.epub"
    
    try:
        success = generator.generate_from_text(
            test_content,
            "误识别测试",
            "测试作者",
            "测试章节误识别修复",
            test_output
        )
        
        if success:
            print(f"✅ 测试EPUB生成成功: {test_output}")
            if os.path.exists(test_output):
                file_size = os.path.getsize(test_output)
                print(f"   文件大小: {file_size} 字节")
        else:
            print("❌ EPUB生成失败")
            
    except Exception as e:
        print(f"❌ 生成过程出错: {e}")
    
    print("\n🎉 误识别修复测试完成!")


if __name__ == "__main__":
    test_misidentification_fix()
